//@version=4
//
// WaveTrend高胜率策略 [优化版本]
// 原作者: LazyBear
// 策略改编: 多重过滤器提高胜率
//
strategy(title="WaveTrend高胜率策略", shorttitle="WT_HIGH_WIN", overlay=false,
         default_qty_type=strategy.percent_of_equity, default_qty_value=100,
         commission_type=strategy.commission.percent, commission_value=0.1,
         pyramiding=0, calc_on_every_tick=false)

// ==================== 输入参数 ====================

// === WaveTrend参数 ===
n1 = input(10, title="通道长度", minval=1, group="WaveTrend设置")
n2 = input(21, title="平均长度", minval=1, group="WaveTrend设置")
obLevel1 = input(60, title="超买区域1", minval=0, group="WaveTrend设置")
obLevel2 = input(53, title="超买区域2", minval=0, group="WaveTrend设置")
osLevel1 = input(-60, title="超卖区域1", maxval=0, group="WaveTrend设置")
osLevel2 = input(-53, title="超卖区域2", maxval=0, group="WaveTrend设置")

// === 趋势过滤器参数 ===
useTrendFilter = input(true, title="启用趋势过滤器", group="趋势过滤")
ema_fast = input(21, title="快速EMA", minval=1, group="趋势过滤")
ema_slow = input(50, title="慢速EMA", minval=1, group="趋势过滤")
ema_trend = input(200, title="趋势EMA", minval=1, group="趋势过滤")

// === 动量确认参数 ===
useMomentumFilter = input(true, title="启用动量过滤器", group="动量确认")
rsi_length = input(14, title="RSI周期", minval=1, group="动量确认")
rsi_oversold = input(30, title="RSI超卖线", minval=1, maxval=50, group="动量确认")
rsi_overbought = input(70, title="RSI超买线", minval=50, maxval=99, group="动量确认")

// === 市场结构参数 ===
useStructureFilter = input(true, title="启用市场结构过滤", group="市场结构")
swing_length = input(5, title="摆动点周期", minval=3, group="市场结构")
breakout_confirm = input(true, title="需要突破确认", group="市场结构")

// === 风险管理参数 ===
riskPercent = input(1.0, title="风险百分比 (%)", minval=0.1, maxval=10.0, group="风险管理") / 100
profitRatio = input(2.5, title="盈亏比", minval=1.0, maxval=5.0, group="风险管理")
useTrailingStop = input(true, title="启用移动止损", group="风险管理")
trailingPercent = input(1.5, title="移动止损百分比", minval=0.5, maxval=5.0, group="风险管理") / 100

// === WaveTrend计算 ===
ap = hlc3 
esa = ema(ap, n1)
d = ema(abs(ap - esa), n1)
ci = (ap - esa) / (0.015 * d)
tci = ema(ci, n2)
 
wt1 = tci
wt2 = sma(wt1, 4)

// === 交易信号 ===
// 基础交叉信号
bullishCross = crossover(wt1, wt2)
bearishCross = crossunder(wt1, wt2)

// 过滤条件
oversoldCondition = useOversoldFilter ? wt2 <= osLevel2 : true
overboughtCondition = useOverboughtFilter ? wt2 >= obLevel2 : true

// 最终交易信号
longSignal = bullishCross and oversoldCondition
shortSignal = bearishCross and overboughtCondition

// === 风险管理计算 ===
// 基于ATR计算止损距离
atrLength = input(14, title="ATR周期", minval=1)
atrMultiplier = input(1.5, title="ATR倍数", minval=0.5, maxval=5.0)
atr = atr(atrLength)

// 动态止损距离
stopLossDistance = atr * atrMultiplier

// === 交易执行 ===
var float longStopLoss = na
var float longTakeProfit = na
var float shortStopLoss = na
var float shortTakeProfit = na

// 做多交易
if longSignal and strategy.position_size == 0
    longStopLoss := close - stopLossDistance
    longTakeProfit := close + (stopLossDistance * profitRatio)
    strategy.entry("做多", strategy.long)
    strategy.exit("多单止损止盈", "做多", stop=longStopLoss, limit=longTakeProfit)

// 做空交易
if shortSignal and strategy.position_size == 0
    shortStopLoss := close + stopLossDistance
    shortTakeProfit := close - (stopLossDistance * profitRatio)
    strategy.entry("做空", strategy.short)
    strategy.exit("空单止损止盈", "做空", stop=shortStopLoss, limit=shortTakeProfit)

// === 可视化显示 ===
// 绘制水平线
hline(0, color=color.gray, linestyle=hline.style_solid)
hline(obLevel1, color=color.red, linestyle=hline.style_dashed, title="超买线1")
hline(osLevel1, color=color.green, linestyle=hline.style_dashed, title="超卖线1")
hline(obLevel2, color=color.red, linestyle=hline.style_dotted, title="超买线2")
hline(osLevel2, color=color.green, linestyle=hline.style_dotted, title="超卖线2")

// 绘制WaveTrend线
plot(wt1, color=color.green, linewidth=2, title="WT1")
plot(wt2, color=color.red, linewidth=2, title="WT2")

// 绘制差值区域
plot(wt1-wt2, color=color.blue, style=plot.style_area, transp=80, title="WT差值")

// 绘制交叉点
plotshape(bullishCross, style=shape.circle, location=location.absolute, 
          color=color.lime, size=size.small, title="看涨交叉")
plotshape(bearishCross, style=shape.circle, location=location.absolute, 
          color=color.red, size=size.small, title="看跌交叉")

// 绘制交易信号
plotshape(longSignal, style=shape.triangleup, location=location.bottom, 
          color=color.green, size=size.normal, title="做多信号")
plotshape(shortSignal, style=shape.triangledown, location=location.top, 
          color=color.red, size=size.normal, title="做空信号")

// === 信息面板 ===
// 显示当前状态
var table infoTable = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
if barstate.islast
    table.cell(infoTable, 0, 0, "指标", text_color=color.black, bgcolor=color.gray)
    table.cell(infoTable, 1, 0, "数值", text_color=color.black, bgcolor=color.gray)
    table.cell(infoTable, 0, 1, "WT1", text_color=color.black)
    table.cell(infoTable, 1, 1, tostring(wt1, "#.##"), text_color=color.black)
    table.cell(infoTable, 0, 2, "WT2", text_color=color.black)
    table.cell(infoTable, 1, 2, tostring(wt2, "#.##"), text_color=color.black)
    table.cell(infoTable, 0, 3, "差值", text_color=color.black)
    table.cell(infoTable, 1, 3, tostring(wt1-wt2, "#.##"), text_color=color.black)
    table.cell(infoTable, 0, 4, "持仓", text_color=color.black)
    table.cell(infoTable, 1, 4, strategy.position_size > 0 ? "做多" : strategy.position_size < 0 ? "做空" : "空仓", 
               text_color=strategy.position_size > 0 ? color.green : strategy.position_size < 0 ? color.red : color.black)

// === 背景颜色 ===
// 根据交叉信号改变背景颜色
bgcolor(longSignal ? color.new(color.green, 90) : shortSignal ? color.new(color.red, 90) : na)
