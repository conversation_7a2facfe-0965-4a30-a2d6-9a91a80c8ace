//@version=4
//
// WaveTrend修复版策略 - 确保信号正常生成
// 原作者: LazyBear
// 策略改编: 简化逻辑确保信号生成
//
strategy(title="WaveTrend修复版", shorttitle="WT_FIXED", overlay=false, 
         default_qty_type=strategy.percent_of_equity, default_qty_value=100,
         commission_type=strategy.commission.percent, commission_value=0.1,
         pyramiding=0, calc_on_every_tick=false)

// ==================== 输入参数 ====================

// === WaveTrend参数 ===
n1 = input(10, title="通道长度", minval=1, group="WaveTrend设置")
n2 = input(21, title="平均长度", minval=1, group="WaveTrend设置")
obLevel1 = input(60, title="超买区域1", minval=0, group="WaveTrend设置")
obLevel2 = input(53, title="超买区域2", minval=0, group="WaveTrend设置")
osLevel1 = input(-60, title="超卖区域1", maxval=0, group="WaveTrend设置")
osLevel2 = input(-53, title="超卖区域2", maxval=0, group="WaveTrend设置")

// === 过滤器开关 ===
useTrendFilter = input(false, title="启用趋势过滤器", group="过滤设置")
useMomentumFilter = input(false, title="启用动量过滤器", group="过滤设置")
useOverboughtFilter = input(true, title="使用超买区域过滤", group="过滤设置")
useOversoldFilter = input(true, title="使用超卖区域过滤", group="过滤设置")

// === 趋势过滤器参数 ===
ema_fast = input(21, title="快速EMA", minval=1, group="趋势过滤")
ema_slow = input(50, title="慢速EMA", minval=1, group="趋势过滤")

// === 动量确认参数 ===
rsi_length = input(14, title="RSI周期", minval=1, group="动量确认")
rsi_oversold = input(30, title="RSI超卖线", minval=1, maxval=50, group="动量确认")
rsi_overbought = input(70, title="RSI超买线", minval=50, maxval=99, group="动量确认")

// === 风险管理参数 ===
riskPercent = input(1.0, title="风险百分比 (%)", minval=0.1, maxval=10.0, group="风险管理") / 100
profitRatio = input(2.0, title="盈亏比", minval=1.0, maxval=5.0, group="风险管理")
atrLength = input(14, title="ATR周期", minval=1, group="风险管理")
atrMultiplier = input(1.5, title="ATR倍数", minval=0.5, maxval=5.0, group="风险管理")

// === 调试设置 ===
debug_mode = input(true, title="启用调试模式", group="调试设置")
show_signals = input(true, title="显示所有信号", group="调试设置")

// ==================== 指标计算 ====================

// === WaveTrend计算 ===
ap = hlc3 
esa = ema(ap, n1)
d = ema(abs(ap - esa), n1)
ci = (ap - esa) / (0.015 * d)
tci = ema(ci, n2)
 
wt1 = tci
wt2 = sma(wt1, 4)

// === 趋势过滤器计算 ===
ema21 = ema(close, ema_fast)
ema50 = ema(close, ema_slow)

// 简化趋势判断
uptrend = ema21 > ema50 and close > ema21
downtrend = ema21 < ema50 and close < ema21

// === 动量确认计算 ===
rsi = rsi(close, rsi_length)

// === 基础交易信号 ===
bullishCross = crossover(wt1, wt2)
bearishCross = crossunder(wt1, wt2)

// === 过滤条件 ===
// WaveTrend位置过滤
wt_oversold_condition = useOversoldFilter ? wt2 <= osLevel2 : true
wt_overbought_condition = useOverboughtFilter ? wt2 >= obLevel2 : true

// 趋势过滤
trend_long_ok = useTrendFilter ? uptrend : true
trend_short_ok = useTrendFilter ? downtrend : true

// 动量过滤
momentum_long_ok = useMomentumFilter ? rsi < rsi_overbought : true
momentum_short_ok = useMomentumFilter ? rsi > rsi_oversold : true

// === 最终交易信号 ===
longCondition = bullishCross and wt_oversold_condition and trend_long_ok and momentum_long_ok
shortCondition = bearishCross and wt_overbought_condition and trend_short_ok and momentum_short_ok

// 确保只在没有持仓时开仓
longSignal = longCondition and strategy.position_size == 0
shortSignal = shortCondition and strategy.position_size == 0

// ==================== 风险管理 ====================

// ATR计算
atr_value = atr(atrLength)
stopLossDistance = atr_value * atrMultiplier

// ==================== 交易执行 ====================

// 做多交易
if longSignal
    stopLoss = close - stopLossDistance
    takeProfit = close + (stopLossDistance * profitRatio)
    strategy.entry("做多", strategy.long)
    strategy.exit("多单止损止盈", "做多", stop=stopLoss, limit=takeProfit)

// 做空交易
if shortSignal
    stopLoss = close + stopLossDistance
    takeProfit = close - (stopLossDistance * profitRatio)
    strategy.entry("做空", strategy.short)
    strategy.exit("空单止损止盈", "做空", stop=stopLoss, limit=takeProfit)

// ==================== 可视化显示 ====================

// 绘制水平线
hline(0, color=color.gray, linestyle=hline.style_solid)
hline(obLevel1, color=color.red, linestyle=hline.style_dashed, title="超买线1")
hline(osLevel1, color=color.green, linestyle=hline.style_dashed, title="超卖线1")
hline(obLevel2, color=color.red, linestyle=hline.style_dotted, title="超买线2")
hline(osLevel2, color=color.green, linestyle=hline.style_dotted, title="超卖线2")

// 绘制WaveTrend线
plot(wt1, color=color.green, linewidth=2, title="WT1")
plot(wt2, color=color.red, linewidth=2, title="WT2")

// 绘制差值区域
plot(wt1-wt2, color=color.blue, style=plot.style_area, transp=80, title="WT差值")

// 绘制交叉点
if show_signals
    plotshape(bullishCross, style=shape.circle, location=location.absolute, 
              color=color.lime, size=size.small, title="看涨交叉")
    plotshape(bearishCross, style=shape.circle, location=location.absolute, 
              color=color.red, size=size.small, title="看跌交叉")

// 绘制交易信号
plotshape(longSignal, style=shape.triangleup, location=location.bottom, 
          color=color.green, size=size.normal, title="做多信号")
plotshape(shortSignal, style=shape.triangledown, location=location.top, 
          color=color.red, size=size.normal, title="做空信号")

// === 调试信息 ===
if debug_mode
    // 在图表上显示关键信息
    var table debugTable = table.new(position.top_left, 2, 8, bgcolor=color.white, border_width=1)
    if barstate.islast
        table.cell(debugTable, 0, 0, "指标", text_color=color.black, bgcolor=color.gray)
        table.cell(debugTable, 1, 0, "数值", text_color=color.black, bgcolor=color.gray)
        
        table.cell(debugTable, 0, 1, "WT1", text_color=color.black)
        table.cell(debugTable, 1, 1, tostring(wt1, "#.##"), text_color=color.black)
        
        table.cell(debugTable, 0, 2, "WT2", text_color=color.black)
        table.cell(debugTable, 1, 2, tostring(wt2, "#.##"), text_color=color.black)
        
        table.cell(debugTable, 0, 3, "金叉", text_color=color.black)
        table.cell(debugTable, 1, 3, bullishCross ? "是" : "否", 
                   text_color=bullishCross ? color.green : color.red)
        
        table.cell(debugTable, 0, 4, "死叉", text_color=color.black)
        table.cell(debugTable, 1, 4, bearishCross ? "是" : "否", 
                   text_color=bearishCross ? color.red : color.green)
        
        table.cell(debugTable, 0, 5, "超卖", text_color=color.black)
        table.cell(debugTable, 1, 5, wt_oversold_condition ? "是" : "否", 
                   text_color=wt_oversold_condition ? color.green : color.red)
        
        table.cell(debugTable, 0, 6, "超买", text_color=color.black)
        table.cell(debugTable, 1, 6, wt_overbought_condition ? "是" : "否", 
                   text_color=wt_overbought_condition ? color.red : color.green)
        
        table.cell(debugTable, 0, 7, "持仓", text_color=color.black)
        table.cell(debugTable, 1, 7, strategy.position_size > 0 ? "多" : strategy.position_size < 0 ? "空" : "无", 
                   text_color=strategy.position_size > 0 ? color.green : strategy.position_size < 0 ? color.red : color.gray)

// 背景颜色
bgcolor(longSignal ? color.new(color.green, 90) : shortSignal ? color.new(color.red, 90) : na)

// === 性能统计 ===
// 在图表上显示策略统计信息
var table statsTable = table.new(position.bottom_right, 2, 5, bgcolor=color.white, border_width=1)
if barstate.islast
    table.cell(statsTable, 0, 0, "统计", text_color=color.black, bgcolor=color.gray)
    table.cell(statsTable, 1, 0, "数值", text_color=color.black, bgcolor=color.gray)
    
    table.cell(statsTable, 0, 1, "总交易", text_color=color.black)
    table.cell(statsTable, 1, 1, tostring(strategy.closedtrades), text_color=color.black)
    
    table.cell(statsTable, 0, 2, "盈利交易", text_color=color.black)
    table.cell(statsTable, 1, 2, tostring(strategy.wintrades), text_color=color.green)
    
    table.cell(statsTable, 0, 3, "亏损交易", text_color=color.black)
    table.cell(statsTable, 1, 3, tostring(strategy.losstrades), text_color=color.red)
    
    winRate = strategy.closedtrades > 0 ? strategy.wintrades / strategy.closedtrades * 100 : 0
    table.cell(statsTable, 0, 4, "胜率", text_color=color.black)
    table.cell(statsTable, 1, 4, tostring(winRate, "#.#") + "%", 
               text_color=winRate >= 50 ? color.green : color.red)
